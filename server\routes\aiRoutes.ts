import express, { Request, Response } from "express";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import {
  FlashcardDeck,
  Flashcard,
  GenerateFlashcardsRequest,
  GenerateFlashcardsResponse,
} from "@shared/types/flashcards";

const router = express.Router();

// POST /api/flashcards/generate
// Changed from /generate-flashcards to /flashcards/generate to match client expectations
router.post("/flashcards/generate", async (req: Request, res: Response) => {
  console.log("Received request at /flashcards/generate:", req.body);

  const { textContent, documentId, deckTitle, count, customPrompt, aiSettings } = req.body;

  if (!textContent || !documentId || !aiSettings || !aiSettings.apiKey) {
    return res.status(400).json({
      error:
        "Missing required fields. Need textContent, documentId, and aiSettings with apiKey.",
    });
  }

  // Use the user-specified count or default to 10
  const flashcardCount = count && count > 0 ? count : 10;

  const { provider, baseUrl, apiKey } = aiSettings;

  // Prefer the generationModel for content generation as specified in the PRD
  // Fall back to model for backward compatibility, or use a recommended default
  const model =
    aiSettings.generationModel ||
    aiSettings.model ||
    "google/gemini-2.5-pro-preview";

  console.log(
    `Generating flashcards for document ${documentId} using model ${model} via ${provider} at ${baseUrl}`
  );

  // Build the prompt with optional custom instructions
  let prompt = `Based on the following text, generate exactly ${flashcardCount} flashcards (question and answer pairs).
Each flashcard should focus on a key concept, term, or fact from the text.

Text:
"""
${textContent}
"""

Guidelines for flashcard generation:
1. Create exactly ${flashcardCount} flashcards
2. Questions should be clear and specific
3. Answers should be concise but complete
4. Focus on the most important concepts
5. Include a mix of definition-based and application-based cards
6. Make sure questions test understanding, not just memorization
7. If the text content is limited, prioritize the most essential concepts`;

  // Add custom prompt instructions if provided
  if (customPrompt && customPrompt.trim()) {
    prompt += `\n\nAdditional Instructions:
${customPrompt.trim()}`;
  }

  prompt += `\n\nResponse Format:
Return a JSON object containing a "flashcards" array with exactly ${flashcardCount} elements, where each element has a "question" and "answer" property.
Example: {"flashcards": [{"question": "What is anthropology?", "answer": "The study of human cultures and societies."}]}
`;

  try {
    const response = await axios.post(
      `${baseUrl}/chat/completions`,
      {
        model: model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.5,
        response_format: { type: "json_object" },
      },
      {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://chewyai.app",
          "X-Title": "ChewyAI",
        },
      }
    );

    console.log("API response received:", response.status);

    let rawFlashcards = [];
    if (
      response.data &&
      response.data.choices &&
      response.data.choices[0]?.message?.content
    ) {
      const content = response.data.choices[0].message.content;
      console.log(
        "Raw AI Response (first 100 chars):",
        content.substring(0, 100)
      );

      try {
        // Parse the JSON response
        const parsedContent = JSON.parse(content);

        if (
          parsedContent.flashcards &&
          Array.isArray(parsedContent.flashcards)
        ) {
          rawFlashcards = parsedContent.flashcards;
        } else {
          console.error(
            "Parsed content does not contain a valid flashcards array:",
            parsedContent
          );
          throw new Error("Invalid flashcard structure in AI response");
        }
      } catch (parseError) {
        console.error("Failed to parse AI response JSON:", parseError);

        // Try to extract JSON from markdown code blocks as fallback
        try {
          const jsonMatch =
            content.match(/```json\n([\s\S]*?)\n```/) ||
            content.match(/```\n([\s\S]*?)\n```/) ||
            content.match(/{[\s\S]*}/);

          if (jsonMatch) {
            const jsonContent = jsonMatch[1] || jsonMatch[0];
            const parsedContent = JSON.parse(jsonContent);

            if (
              parsedContent.flashcards &&
              Array.isArray(parsedContent.flashcards)
            ) {
              rawFlashcards = parsedContent.flashcards;
            }
          }
        } catch (e) {
          console.error("Failed fallback parsing:", e);
        }

        if (rawFlashcards.length === 0) {
          return res
            .status(500)
            .json({ error: "Failed to parse flashcards from AI response" });
        }
      }
    } else {
      console.error("Unexpected AI response structure:", response.data);
      return res
        .status(500)
        .json({ error: "Invalid response structure from AI provider" });
    }

    // Create a flashcard deck using the format expected by the client
    const deckId = uuidv4();
    const flashcardDeck: FlashcardDeck = {
      id: deckId,
      documentId: documentId,
      title: deckTitle || `Flashcards for ${documentId}`,
      flashcards: rawFlashcards.map((fc) => ({
        id: uuidv4(),
        question: fc.question,
        answer: fc.answer,
        deckId: deckId,
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Return in the format expected by client (using deck property)
    const responsePayload: GenerateFlashcardsResponse = {
      deck: flashcardDeck,
    };

    console.log(
      `Successfully generated deck with ${flashcardDeck.flashcards.length} flashcards.`
    );
    return res.status(201).json(responsePayload);
  } catch (error: any) {
    console.error(
      "Error calling AI provider:",
      error.response?.data || error.message
    );
    return res.status(500).json({
      error: "Failed to generate flashcards from AI provider",
      details: error.response?.data?.error || error.message,
    });
  }
});

// POST /api/extract-and-format
// AI-powered text extraction and markdown formatting using Gemini 2.5 Flash
router.post("/extract-and-format", async (req: Request, res: Response) => {
  console.log("Received request at /extract-and-format:", req.body);

  const { rawText, fileName, aiSettings } = req.body;

  if (!rawText || !aiSettings || !aiSettings.apiKey) {
    return res.status(400).json({
      error: "Missing required fields. Need rawText and aiSettings with apiKey.",
    });
  }

  const { provider, baseUrl, apiKey } = aiSettings;

  // Use the extractionModel for text processing as specified in the PRD
  // Fall back to generationModel, then model for backward compatibility
  const model =
    aiSettings.extractionModel ||
    aiSettings.generationModel ||
    aiSettings.model ||
    "google/gemini-2.5-flash";

  console.log(
    `Processing text extraction and formatting for ${fileName} using model ${model} via ${provider} at ${baseUrl}`
  );

  // Create AI prompt for text extraction and formatting
  const prompt = `You are an expert document processor specialized in converting raw extracted text into clean, well-formatted, visually appealing Markdown. Your goal is to preserve the document's structure and meaning while making it highly readable, legible, and aesthetically pleasing.

Document: ${fileName}

CRITICAL INSTRUCTIONS FOR BEAUTIFUL, LEGIBLE MARKDOWN:

🎯 **PRIMARY GOALS:**
- Transform raw text into visually stunning, highly readable markdown
- Create clear visual hierarchy with proper spacing and formatting
- Make content scannable and aesthetically pleasing

📝 **FORMATTING EXCELLENCE:**
1. **Document Structure:**
   - Use clear heading hierarchy (# ## ### ####) with descriptive titles
   - Add proper spacing between sections (double line breaks)
   - Create logical content flow with smooth transitions

2. **Visual Enhancement:**
   - **Bold** key terms, definitions, and important concepts
   - *Italicize* emphasis, foreign terms, and book/article titles
   - Use `inline code` for technical terms, variables, and specific values
   - Create > blockquotes for important notes, definitions, or key insights

3. **List Optimization:**
   - Convert bullet points (●, ○, •) to clean markdown bullets (-)
   - Use numbered lists (1. 2. 3.) for sequential information
   - Add proper spacing between list items for readability
   - Nest sub-items with proper indentation

4. **Content Cleanup:**
   - Fix OCR errors, broken words, and formatting artifacts
   - Remove excessive whitespace while maintaining readability
   - Ensure proper sentence structure and paragraph flow
   - Add line breaks between distinct concepts or topics

5. **Academic/Technical Preservation:**
   - Keep all technical terms, formulas, and data intact
   - Preserve citation formats and references
   - Maintain numerical data and statistics accuracy
   - Convert tables to proper markdown table format

6. **Visual Separators:**
   - Use horizontal rules (---) to separate major sections
   - Add appropriate spacing around headings and sections
   - Ensure consistent formatting throughout the document

Raw Text (first 50,000 characters):
---
${rawText.substring(0, 50000)}
---

✨ **FINAL REQUIREMENTS:**
- Output should be visually stunning and professional
- Every element should be properly spaced and formatted
- Content should be immediately scannable and easy to read
- Use markdown features to create visual interest and clarity

Return ONLY the beautifully formatted Markdown content. Do not include explanations, metadata, or commentary.`;

  try {
    const response = await axios.post(
      `${baseUrl}/chat/completions`,
      {
        model: model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.1, // Low temperature for consistent formatting
        max_tokens: 12000, // Increased for beautifully formatted content
      },
      {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://chewyai.app",
          "X-Title": "ChewyAI",
        },
      }
    );

    console.log("AI extraction response received:", response.status);

    if (response.data && response.data.choices && response.data.choices[0]) {
      const formattedContent = response.data.choices[0].message.content;

      console.log("Successfully processed text extraction and formatting");
      return res.status(200).json({
        success: true,
        formattedContent: formattedContent.trim(),
        originalLength: rawText.length,
        formattedLength: formattedContent.length,
      });
    } else {
      console.error("Unexpected AI response structure:", response.data);
      return res
        .status(500)
        .json({ error: "Invalid response structure from AI provider" });
    }
  } catch (error: any) {
    console.error(
      "Error calling AI provider for text extraction:",
      error.response?.data || error.message
    );
    return res.status(500).json({
      error: "Failed to process text extraction with AI provider",
      details: error.response?.data?.error || error.message,
    });
  }
});

export default router;
